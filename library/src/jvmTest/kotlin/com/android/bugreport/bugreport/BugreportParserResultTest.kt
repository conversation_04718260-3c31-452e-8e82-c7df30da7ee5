/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.bugreport.bugreport

import com.android.bugreport.util.Line
import com.android.bugreport.util.Lines
import com.android.bugreport.util.Result
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * Test for the Result-based BugreportParser refactoring.
 */
class BugreportParserResultTest {

    @Test
    fun testParseEmptyInput() {
        val parser = BugreportParser()
        val lines = Lines<Line>(emptyList())

        val result = parser.parse(lines)

        assertTrue(result.isSuccess, "Parsing empty input should succeed")
        val bugreport = result.getOrThrow()
        assertEquals(null, bugreport.buildId)
        assertEquals(null, bugreport.startTime)
    }

    @Test
    fun testParseReturnsResult() {
        val parser = BugreportParser()
        val lineList = listOf(
            Line(1, "Build: test-build-123"),
            Line(2, "some content")
        )
        val lines = Lines<Line>(lineList)

        val result = parser.parse(lines)

        // The key test is that we get a Result type back, not the specific content
        assertTrue(result.isSuccess || result.isFailure, "Should return a Result")

        // If it succeeds, we should get a Bugreport
        result.onSuccess { bugreport ->
            assertTrue(bugreport is Bugreport, "Success should contain a Bugreport")
        }

        // If it fails, we should get a ParseError
        result.onFailure { error ->
            assertTrue(error is ParseError, "Failure should contain a ParseError")
        }
    }

    @Test
    fun testParseErrorTypes() {
        // Test that our ParseError types are properly defined
        val fileError = ParseError.FileReadError("test.txt", RuntimeException("IO error"))
        assertEquals("Failed to read file: test.txt", fileError.message)
        
        val sectionError = ParseError.SectionParseError("SYSTEM LOG", "Invalid format")
        assertEquals("Failed to parse section 'SYSTEM LOG': Invalid format", sectionError.message)
        
        val formatError = ParseError.FormatError("Invalid timestamp", 42)
        assertEquals("Invalid format at line 42: Invalid timestamp", formatError.message)
        
        val missingError = ParseError.MissingDataError("timestamp")
        assertEquals("Missing required data: timestamp", missingError.message)
        
        val genericError = ParseError.GenericParseError("Unknown error")
        assertEquals("Parse error: Unknown error", genericError.message)
    }

    @Test
    fun testResultChaining() {
        // Test that Result chaining works as expected
        val successResult: Result<String> = Result.success("test")
        val mappedResult = successResult.map { it.uppercase() }

        assertTrue(mappedResult.isSuccess)
        assertEquals("TEST", mappedResult.getOrNull())

        val failureResult: Result<String> = Result.failure(ParseError.GenericParseError("test error"))
        val mappedFailure = failureResult.map { it.uppercase() }

        assertTrue(mappedFailure.isFailure)
        assertTrue(mappedFailure.exceptionOrNull() is ParseError.GenericParseError)
    }

    @Test
    fun testCustomResultMetadata() {
        val parser = BugreportParser()
        val lines = Lines<Line>(emptyList())

        val result = parser.parse(lines)

        // Test that metadata is present
        val metadata = result.metadata
        assertTrue(metadata.startTime != null, "Should have start time")
        assertTrue(metadata.endTime != null, "Should have end time")
        assertTrue(metadata.calculateDuration() != null, "Should have duration")
        assertEquals(0, metadata.linesProcessed, "Should have processed 0 lines")

        // Test context information
        assertTrue(metadata.context.containsKey("totalLines"), "Should have totalLines context")
        assertTrue(metadata.context.containsKey("parser"), "Should have parser context")
    }
}
