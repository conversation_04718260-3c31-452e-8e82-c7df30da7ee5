/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.bugreport.section

import com.android.bugreport.bugreport.ParseError
import com.android.bugreport.logcat.LogcatParseResult
import com.android.bugreport.logcat.LogcatParser
import com.android.bugreport.util.Line
import com.android.bugreport.util.Lines
import com.android.bugreport.util.Result

/**
 * Section parser for logcat sections (SYSTEM LOG and EVENT LOG).
 */
class LogcatSectionParser : SectionParser {
    private val parser = LogcatParser()

    override fun getSectionNames(): Array<String> {
        return arrayOf("SYSTEM LOG", "EVENT LOG")
    }

    override fun parse(section: String, command: String?, lines: Lines<out Line>): SectionParseResult {
        return try {
            when (section) {
                "SYSTEM LOG" -> {
                    val result = parser.parse(lines)
                    when (result) {
                        is LogcatParseResult.LogcatParseSuccess -> {
                            SectionParseResult.SystemLogSuccess(
                                result.logcat,
                            )
                        }
                        is LogcatParseResult.LogcatParseFailure -> {
                            SectionParseResult.SectionParseFailure(
                                ParseError.SectionParseError(section, "Failed to parse system log", result.error)
                            )
                        }
                    }
                }
                "EVENT LOG" -> {
                    val result = parser.parse(lines)
                    when (result) {
                        is LogcatParseResult.LogcatParseSuccess -> {
                            SectionParseResult.EventLogSuccess(
                                result.logcat
                            )
                        }
                        is LogcatParseResult.LogcatParseFailure -> {
                            SectionParseResult.SectionParseFailure(
                                ParseError.SectionParseError(section, "Failed to parse event log", result.error)
                            )
                        }
                    }
                }
                else -> SectionParseResult.SectionParseFailure(
                    ParseError.SectionParseError(section, "Unknown section")
                )
            }
        } catch (e: Exception) {
            SectionParseResult.SectionParseFailure(
                ParseError.SectionParseError(section, "Unexpected error during parsing", e)
            )
        }
    }
}
