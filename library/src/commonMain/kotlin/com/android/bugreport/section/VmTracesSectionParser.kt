/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.bugreport.section

import com.android.bugreport.bugreport.ParseError
import com.android.bugreport.stacks.VmTracesParseResult
import com.android.bugreport.stacks.VmTracesParser
import com.android.bugreport.util.Line
import com.android.bugreport.util.Lines

/**
 * Section parser for VM traces sections (VM TRACES JUST NOW and VM TRACES AT LAST ANR).
 */
class VmTracesSectionParser : SectionParser {
    private val parser = VmTracesParser()

    override fun getSectionNames(): Array<String> {
        return arrayOf("VM TRACES JUST NOW", "VM TRACES AT LAST ANR")
    }

    override fun parse(
        section: String,
        command: String?,
        lines: Lines<out Line>
    ): SectionParseResult {
        return try {
            when (section) {
                "VM TRACES JUST NOW" -> {
                    val result = parser.parse(lines)
                    when (result) {
                        is VmTracesParseResult.VmTracesParseSuccess -> {
                            SectionParseResult.VmTracesJustNowSuccess(
                                result.vmTraces
                            )
                        }

                        is VmTracesParseResult.VmTracesParseFailure -> {
                            SectionParseResult.SectionParseFailure(
                                ParseError.SectionParseError(
                                    section,
                                    "Failed to parse VM traces",
                                    result.error
                                )
                            )
                        }
                    }
                }

                "VM TRACES AT LAST ANR" -> {
                    val result = parser.parse(lines)
                    when (result) {
                        is VmTracesParseResult.VmTracesParseSuccess -> {
                            SectionParseResult.VmTracesLastAnrSuccess(
                                result.vmTraces
                            )
                        }

                        is VmTracesParseResult.VmTracesParseFailure -> {
                            SectionParseResult.SectionParseFailure(
                                ParseError.SectionParseError(
                                    section,
                                    "Failed to parse VM traces at last ANR",
                                    result.error
                                )
                            )
                        }
                    }
                }

                else -> SectionParseResult.SectionParseFailure(
                    ParseError.SectionParseError(section, "Unknown section")
                )
            }
        } catch (e: Exception) {
            SectionParseResult.SectionParseFailure(
                ParseError.SectionParseError(section, "Unexpected error during parsing", e)
            )
        }
    }
}
