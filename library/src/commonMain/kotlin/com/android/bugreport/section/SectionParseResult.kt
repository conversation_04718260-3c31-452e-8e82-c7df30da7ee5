/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.bugreport.section

import com.android.bugreport.logcat.Logcat
import com.android.bugreport.stacks.VmTraces

/**
 * Sealed class representing the result of parsing a section.
 * This is the base class for all section parsing results.
 */
sealed class SectionParseResult {

    /**
     * Returns true if this is a success result.
     */
    val isSuccess: Boolean get() = this !is SectionParseFailure

    /**
     * Returns true if this is a failure result.
     */
    val isFailure: Boolean get() = this is SectionParseFailure

    /**
     * Successful result for system log section parsing.
     */
    data class SystemLogSuccess(
        val logcat: Logcat,
    ) : SectionParseResult()

    /**
     * Successful result for event log section parsing.
     */
    data class EventLogSuccess(
        val logcat: Logcat,
    ) : SectionParseResult()

    /**
     * Successful result for VM traces just now section parsing.
     */
    data class VmTracesJustNowSuccess(
        val vmTraces: VmTraces,
    ) : SectionParseResult()

    /**
     * Successful result for VM traces at last ANR section parsing.
     */
    data class VmTracesLastAnrSuccess(
        val vmTraces: VmTraces,
    ) : SectionParseResult()

    /**
     * Failed result for section parsing.
     */
    data class SectionParseFailure(
        val error: Throwable,
    ) : SectionParseResult()
}
