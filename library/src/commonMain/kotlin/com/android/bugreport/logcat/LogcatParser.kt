/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.bugreport.logcat

import com.android.bugreport.bugreport.ParseError
import com.android.bugreport.util.Line
import com.android.bugreport.util.Lines
import com.android.bugreport.util.Utils

/**
 * Parses a stream of text as a logcat.
 */
class LogcatParser {

    companion object {
        val BUFFER_BEGIN_RE = Regex("--------- beginning of (.*)")
        private val LOG_LINE_RE = Regex(
            "(" + Utils.DATE_TIME_MS_PATTERN +
                "\\s+(\\d+)\\s+(\\d+)\\s+(.)\\s+)(.*?):\\s(.*)"
        )
    }

    /**
     * Parse the logcat lines, returning a LogcatParseResult.
     */
    fun parse(lines: Lines<out Line>): LogcatParseResult {
        return try {
            val metadata = LogcatParseResult.Metadata()
                .addContext("parser", "LogcatParser")
                .copy(linesProcessed = lines.size())

            val result = Logcat()
            var lineno = 0
            var parseErrors = 0
            val warnings = mutableListOf<String>()

            while (lines.hasNext()) {
                val line = lines.next() ?: break
                val text = line.text

                val bufferMatch = Utils.match(BUFFER_BEGIN_RE, text)
                val logMatch = Utils.match(LOG_LINE_RE, text)

                when {
                    bufferMatch != null -> {
                        // Beginning of buffer marker
                        val ll = LogLine().apply {
                            this.lineno = lineno++
                            rawText = text
                            bufferBegin = bufferMatch.groups[1]?.value
                        }
                        result.lines.add(ll)
                    }
                    logMatch != null -> {
                        // Matched line
                        try {
                            val ll = LogLine().apply {
                                this.lineno = lineno++
                                rawText = text
                                header = logMatch.groups[1]?.value
                                time = Utils.parseCalendar(logMatch, 2, true)
                                pid = logMatch.groups[9]?.value?.toInt() ?: -1
                                tid = logMatch.groups[10]?.value?.toInt() ?: -1
                                level = logMatch.groups[11]?.value?.firstOrNull() ?: ' '
                                tag = logMatch.groups[12]?.value
                                this.text = logMatch.groups[13]?.value ?: ""
                            }
                            result.lines.add(ll)

                            if (false) {
                                println("LogLine: time=${ll.time} pid=${ll.pid} tid=${ll.tid} level=${ll.level} tag=${ll.tag} text=${ll.text}")
                            }
                        } catch (e: Exception) {
                            // Log parsing error for this line but continue
                            parseErrors++
                            warnings.add("Error parsing log line at $lineno: ${e.message}")
                            if (false) {
                                println("Error parsing log line at $lineno: ${e.message}")
                            }
                        }
                    }
                    else -> {
                        if (false) {
                            println("\nUNMATCHED: [$text]")
                        }
                    }
                }
            }

            val finalMetadata = metadata.complete()
                .copy(
                    warningCount = warnings.size,
                    warnings = warnings
                )
                .addMetric("totalLogLines", result.lines.size.toDouble())
                .addMetric("parseErrorRate", if (lineno > 0) (parseErrors.toDouble() / lineno) * 100.0 else 0.0)
                .addContext("bufferMarkers", result.lines.count { it.bufferBegin != null })
                .addContext("parseErrors", parseErrors)

            LogcatParseResult.LogcatParseSuccess(result, finalMetadata)
        } catch (e: Exception) {
            LogcatParseResult.LogcatParseFailure(
                ParseError.GenericParseError("Failed to parse logcat", e),
                LogcatParseResult.Metadata().complete().addContext("error", "Unexpected logcat parsing failure")
            )
        }
    }
}
