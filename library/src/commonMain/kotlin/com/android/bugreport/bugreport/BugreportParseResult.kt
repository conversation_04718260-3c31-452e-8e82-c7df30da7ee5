/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.bugreport.bugreport

import kotlinx.datetime.Clock
import kotlinx.datetime.Instant

/**
 * Sealed class representing the result of parsing a bugreport.
 */
sealed class BugreportParseResult {
    
    /**
     * Returns true if this is a success result.
     */
    val isSuccess: Boolean get() = this is BugreportParseSuccess
    
    /**
     * Returns true if this is a failure result.
     */
    val isFailure: Boolean get() = this is BugreportParseFailure
    
    /**
     * Successful result for bugreport parsing.
     */
    data class BugreportParseSuccess(
        val bugreport: Bugreport,
    ) : BugreportParseResult()

    /**
     * Failed result for bugreport parsing.
     */
    data class BugreportParseFailure(
        val error: Throwable,
    ) : BugreportParseResult()
}
