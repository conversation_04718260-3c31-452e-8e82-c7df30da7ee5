/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.bugreport.bugreport

import com.android.bugreport.util.Line
import com.android.bugreport.util.Lines
import com.android.bugreport.util.Result
import com.android.bugreport.util.Utils
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime
import kotlin.time.Duration.Companion.milliseconds

/**
 * Parse the header and footer of the bugreport.
 */
class MetadataParser {

    companion object {
        private val DUMPSTATE_LINE_RE = Regex("== dumpstate: " + Utils.DATE_TIME_PATTERN)
        private val HEADER_LINE_RE = Regex("([^:]+): (.*)")
    }

    private lateinit var bugreport: Bugreport

    /**
     * Set the Bugreport that we're working on.
     */
    fun setBugreport(bugreport: Bugreport) {
        this.bugreport = bugreport
    }

    /**
     * Parse the preamble.
     */
    fun parseHeader(lines: Lines<out Line>): Result<Unit> {
        return try {
            var fieldsFound = 0
            val warnings = mutableListOf<String>()

            while (lines.hasNext()) {
                val line = lines.next() ?: break
                val text = line.text

                val dumpstateMatch = Utils.match(DUMPSTATE_LINE_RE, text)
                val headerMatch = Utils.match(HEADER_LINE_RE, text)

                when {
                    dumpstateMatch != null -> {
                        try {
                            bugreport.startTime = Utils.parseCalendar(dumpstateMatch, 1, false)
                            fieldsFound++
                        } catch (e: Exception) {
                            warnings.add("Invalid date format in dumpstate line at ${line.lineno}: ${e.message}")
                        }
                    }
                    headerMatch != null -> {
                        val key = headerMatch.groups[1]?.value
                        val value = headerMatch.groups[2]?.value
                        if ("Build" == key) {
                            bugreport.buildId = value
                            fieldsFound++
                        }
                    }
                }
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(ParseError.GenericParseError("Failed to parse header", e))
        }
    }

    /**
     * Parse the footer.
     */
    fun parseFooter(lines: Lines<out Line>, durationMs: Int): Result<Unit> {
        return try {
            val warnings = mutableListOf<String>()
            val startTime = bugreport.startTime

            if (startTime != null) {
                // Add duration to start time to get end time
                val startInstant = startTime.toInstant(Utils.UTC)
                val endInstant = startInstant.plus(durationMs.milliseconds)
                bugreport.endTime = endInstant.toLocalDateTime(Utils.UTC)
            } else {
                warnings.add("No start time available to calculate end time")
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(ParseError.GenericParseError("Failed to parse footer", e))
        }
    }
}
