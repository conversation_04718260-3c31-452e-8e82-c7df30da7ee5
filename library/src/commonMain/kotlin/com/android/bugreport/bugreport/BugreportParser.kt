/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.bugreport.bugreport

import com.android.bugreport.section.LogcatSectionParser
import com.android.bugreport.section.SectionParseResult
import com.android.bugreport.section.SectionParser
import com.android.bugreport.section.VmTracesSectionParser
import com.android.bugreport.util.Line
import com.android.bugreport.util.Lines
import com.android.bugreport.util.Utils

/**
 * Sealed class representing different types of parsing errors.
 */
sealed class ParseError(message: String, cause: Throwable? = null) : Exception(message, cause) {
    /**
     * Error when reading or accessing file content.
     */
    data class FileReadError(val fileName: String, override val cause: Throwable) :
        ParseError("Failed to read file: $fileName", cause)

    /**
     * Error when parsing a specific section.
     */
    data class SectionParseError(val sectionName: String, val reason: String, override val cause: Throwable? = null) :
        ParseError("Failed to parse section '$sectionName': $reason", cause)

    /**
     * Error when the file format is invalid or unexpected.
     */
    data class FormatError(val reason: String, val lineNumber: Int? = null) :
        ParseError("Invalid format${lineNumber?.let { " at line $it" } ?: ""}: $reason")

    /**
     * Error when required data is missing.
     */
    data class MissingDataError(val dataType: String) :
        ParseError("Missing required data: $dataType")

    /**
     * Generic parsing error for unexpected issues.
     */
    data class GenericParseError(val reason: String, override val cause: Throwable? = null) :
        ParseError("Parse error: $reason", cause)
}

/**
 * Parses a bugreport text file.  The object can be reused, but can only parse
 * one bugreport at a time (i.e. any single object is not thread-safe).
 */
class BugreportParser {

    companion object {
        private val SECTION_BEGIN = Regex("------ (.*?)(?: \\((.*)\\)) ------")
        private val SECTION_BEGIN_NO_CMD = Regex("------ ([^(]+) ------")
        private val SECTION_END = Regex("------ (\\d+.\\d+)s was the duration of '(.*?)(?: \\(.*\\))?' ------")
    }



    private val sectionParsers = mutableMapOf<String, SectionParser>()
    private val metadataParser = MetadataParser()
    private lateinit var bugreport: Bugreport

    /**
     * Create the list of section parsers. Each one handles one or more sections and returns
     * parsed results that will be applied to the Bugreport.
     */
    private fun createParserList(): Array<SectionParser> {
        return arrayOf(
            LogcatSectionParser(),
            VmTracesSectionParser()
        )
    }

    /**
     * Initialize the section parsers after the bugreport is created.
     */
    private fun initializeSectionParsers() {
        val parserList = createParserList()
        for (parser in parserList) {
            for (name in parser.getSectionNames()) {
                sectionParsers[name] = parser
            }
        }
    }

    /**
     * Parse the input into a Bugreport object.
     */
    fun parse(lines: Lines<out Line>): BugreportParseResult {
        return try {
            bugreport = Bugreport()
            metadataParser.setBugreport(bugreport)
            initializeSectionParsers()

            // Read and parse the preamble -- until the first section beginning
            val pos = lines.pos
            while (lines.hasNext()) {
                val line = lines.next() ?: break
                if (Utils.matches(SECTION_BEGIN, line.text)) {
                    lines.rewind()
                    try {
                        metadataParser.parseHeader(lines.copy(pos, lines.pos))
                    } catch (e: Exception) {
                        return BugreportParseResult.BugreportParseFailure(
                            ParseError.SectionParseError("header", "Failed to parse header", e)
                        )
                    }
                    break
                }
            }

            // Read each section, and then parse it
            var section: String? = null
            var command: String? = null
            var sectionPos = lines.pos
            val parseErrors = mutableListOf<ParseError>()
            var sectionsProcessed = 0
            var totalWarnings = 0
            val warnings = mutableListOf<String>()

            while (lines.hasNext()) {
                val line = lines.next() ?: break

                val endMatch = Utils.match(SECTION_END, line.text)
                val beginMatch = Utils.match(SECTION_BEGIN, line.text) ?: Utils.match(SECTION_BEGIN_NO_CMD, line.text)
            
                when {
                    endMatch != null -> {
                        val durationMs = (endMatch.groups[1]?.value?.toFloat()?.times(1000))?.toInt() ?: -1
                        val endSection = endMatch.groups[2]?.value
                        if (section != null && endSection == section) {
                            // End of the section
                            val parseResult = parseSection(section, lines.copy(sectionPos, lines.pos - 1), command, durationMs)
                            if (parseResult != null) {
                                if (parseResult.isSuccess) {
                                    sectionsProcessed++
                                } else if (parseResult is SectionParseResult.SectionParseFailure) {
                                    parseErrors.add(parseResult.error as? ParseError ?: ParseError.GenericParseError("Section parse failed", parseResult.error))
                                }
                            }
                            sectionPos = lines.pos // for the footer
                            section = null
                        } else {
                            if (false) {
                                println("mismatched end of section=$section endSection=$endSection")
                            }
                            // We missed it. Don't do anything. Keep reading until we find
                            // the right section marker or the beginning of the next section.
                            // Except the last one for the whole bugreport has an extra footer
                            if ("DUMPSTATE" == endSection) {
                                try {
                                    metadataParser.parseFooter(lines.copy(sectionPos, lines.pos - 1), durationMs)
                                } catch (e: Exception) {
                                    parseErrors.add(ParseError.SectionParseError("footer", "Failed to parse footer", e))
                                }
                            }
                        }
                    }
                    beginMatch != null -> {
                        // Beginning of the section
                        // Clean out any section that wasn't closed properly (it happens)
                        if (section != null) {
                            if (false) {
                                println("missed end of section $section")
                            }
                            val parseResult = parseSection(section, lines.copy(sectionPos, lines.pos - 1), null, -1)
                            if (parseResult is SectionParseResult.SectionParseFailure) {
                                parseErrors.add(parseResult.error as? ParseError ?: ParseError.GenericParseError("Section parse failed", parseResult.error))
                            }
                        }
                        section = beginMatch.groups[1]?.value
                        command = if (beginMatch.groups.size > 2) beginMatch.groups[2]?.value else null
                        sectionPos = lines.pos
                    }
                }
            }

            // Return result based on whether there were any critical errors
            if (parseErrors.isNotEmpty()) {
                // For now, we'll return success with warnings logged
                // In a production system, you might want to decide which errors are critical
                if (false) {
                    parseErrors.forEach { error ->
                        println("Parse warning: ${error.message}")
                    }
                }
                BugreportParseResult.BugreportParseSuccess(bugreport)
            } else {
                BugreportParseResult.BugreportParseSuccess(bugreport)
            }
        } catch (e: Exception) {
            BugreportParseResult.BugreportParseFailure(
                ParseError.GenericParseError("Unexpected error during bugreport parsing", e))
        }
    }

    /**
     * Parse a section and apply the result to the bugreport.
     */
    private fun parseSection(section: String, lines: Lines<out Line>, command: String?, durationMs: Int): SectionParseResult? {
        val parser = sectionParsers[section]
        return if (parser != null) {
            if (false) {
                println("Parsing section  '$section' ${lines.size()} lines")
            }
            val parseResult = parser.parse(section, command, lines)

            // Apply the parsed result to the bugreport based on the result type
            when (parseResult) {
                is SectionParseResult.SystemLogSuccess -> {
                    bugreport.systemLog = parseResult.logcat
                }
                is SectionParseResult.EventLogSuccess -> {
                    bugreport.eventLog = parseResult.logcat
                }
                is SectionParseResult.VmTracesJustNowSuccess -> {
                    bugreport.vmTracesJustNow = parseResult.vmTraces
                }
                is SectionParseResult.VmTracesLastAnrSuccess -> {
                    bugreport.vmTracesLastAnr = parseResult.vmTraces
                }
                is SectionParseResult.SectionParseFailure -> {
                    // Return the failure result for error handling
                }
            }
            parseResult
        } else {
            if (false) {
                println("Skipping section '$section' ${lines.size()} lines")
            }
            null
        }
    }
}
