/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.bugreport.examples

import com.android.bugreport.bugreport.BugreportParser
import com.android.bugreport.bugreport.ParseError
import com.android.bugreport.util.Line
import com.android.bugreport.util.Lines
import com.android.bugreport.util.Result

/**
 * Example demonstrating the new Result-based architecture for BugreportParser.
 */
object ResultArchitectureExample {

    /**
     * Example of how to use the new Result-based BugreportParser.
     */
    fun demonstrateResultUsage() {
        val parser = BugreportParser()
        
        // Create some sample input
        val sampleLines = listOf(
            Line(1, "== dumpstate: 2024-01-01 12:00:00"),
            Line(2, "Build: example-build-456"),
            Line(3, "------ SYSTEM LOG ------"),
            Line(4, "01-01 12:00:01.123  1234  5678 I ExampleTag: Sample log message"),
            Line(5, "------ 2.5s was the duration of 'SYSTEM LOG' ------")
        )
        val lines = Lines<Line>(sampleLines)
        
        // Parse using the new Result-based API
        val result = parser.parse(lines)
        
        // Handle the result using functional style
        result.fold(
            onSuccess = { bugreport ->
                println("✅ Parsing succeeded!")
                println("Build ID: ${bugreport.buildId}")
                println("Start Time: ${bugreport.startTime}")
                println("System Log Lines: ${bugreport.systemLog?.lines?.size ?: 0}")
            },
            onFailure = { error ->
                println("❌ Parsing failed!")
                when (error) {
                    is ParseError.FileReadError -> {
                        println("File read error: ${error.fileName}")
                        println("Cause: ${error.cause?.message}")
                    }
                    is ParseError.SectionParseError -> {
                        println("Section parse error: ${error.sectionName}")
                        println("Reason: ${error.reason}")
                    }
                    is ParseError.FormatError -> {
                        println("Format error: ${error.reason}")
                        error.lineNumber?.let { println("At line: $it") }
                    }
                    is ParseError.MissingDataError -> {
                        println("Missing data: ${error.dataType}")
                    }
                    is ParseError.GenericParseError -> {
                        println("Generic error: ${error.reason}")
                    }
                }
            }
        )
        
        // Alternative: Using traditional if/else style
        if (result.isSuccess) {
            val bugreport = result.getOrThrow()
            println("\n📊 Alternative handling - Success:")
            println("Bugreport parsed successfully with ${bugreport.allKnownProcesses.size} processes")
        } else {
            val error = result.exceptionOrNull()
            println("\n⚠️ Alternative handling - Error:")
            println("Error: ${error?.message}")
        }
        
        // Chaining operations with map/flatMap
        val buildIdResult = result.map { bugreport -> 
            bugreport.buildId ?: "unknown" 
        }
        
        buildIdResult.onSuccess { buildId ->
            println("\n🏗️ Build ID extracted: $buildId")
        }

        // Demonstrate metadata usage
        println("\n📊 Parsing Metadata:")
        val metadata = result.metadata
        println("Duration: ${metadata.calculateDuration()}ms")
        println("Lines processed: ${metadata.linesProcessed}")
        println("Warnings: ${metadata.warningCount}")
        println("Context: ${metadata.context}")
        println("Metrics: ${metadata.metrics}")
    }

    /**
     * Example of error handling patterns.
     */
    fun demonstrateErrorHandling() {
        println("\n🔍 Error Handling Examples:")
        
        // Example 1: File read error
        val fileError = ParseError.FileReadError("missing-file.txt", RuntimeException("File not found"))
        println("File Error: ${fileError.message}")
        
        // Example 2: Section parse error
        val sectionError = ParseError.SectionParseError("SYSTEM LOG", "Invalid log format", 
            RuntimeException("Regex mismatch"))
        println("Section Error: ${sectionError.message}")
        
        // Example 3: Format error with line number
        val formatError = ParseError.FormatError("Invalid timestamp format", 42)
        println("Format Error: ${formatError.message}")
        
        // Example 4: Missing data error
        val missingError = ParseError.MissingDataError("start timestamp")
        println("Missing Data Error: ${missingError.message}")
        
        // Example 5: Generic error
        val genericError = ParseError.GenericParseError("Unexpected parsing failure", 
            RuntimeException("Unknown cause"))
        println("Generic Error: ${genericError.message}")
    }

    /**
     * Example of composing multiple parsing operations.
     */
    fun demonstrateComposition() {
        println("\n🔗 Composition Examples:")
        
        // Simulate multiple parsing steps
        val step1: Result<String> = Result.success("raw-data")
        val step2: Result<String> = step1.map { it.uppercase() }
        val step3: Result<Int> = step2.map { it.length }
        
        step3.fold(
            onSuccess = { length -> println("Final result: $length characters") },
            onFailure = { error -> println("Composition failed: ${error.message}") }
        )
        
        // Error propagation example
        val failingStep: Result<String> = Result.failure(ParseError.GenericParseError("Step failed"))
        val chainedResult = failingStep
            .map { it.uppercase() }
            .map { it.length }
        
        println("Chained result is failure: ${chainedResult.isFailure}")
    }

    /**
     * Example of custom Result features like metadata and performance tracking.
     */
    fun demonstrateCustomResultFeatures() {
        println("\n🔧 Custom Result Features:")

        // Create a result with custom metadata
        val metadata = Result.Metadata()
            .addContext("operation", "example")
            .addContext("version", "1.0")
            .addMetric("performance", 95.5)
            .addWarning("This is a test warning")

        val result = Result.success("Custom result with metadata", metadata.complete())

        println("Result value: ${result.getOrNull()}")
        println("Duration: ${result.metadata.calculateDuration()}ms")
        println("Warnings: ${result.metadata.warnings}")
        println("Context: ${result.metadata.context}")
        println("Metrics: ${result.metadata.metrics}")

        // Demonstrate metadata combination in flatMap
        val combinedResult = result.flatMap { value ->
            val newMetadata = Result.Metadata()
                .addContext("step", "transformation")
                .addMetric("transformationTime", 12.3)
            Result.success(value.uppercase(), newMetadata)
        }

        println("\nAfter flatMap:")
        println("Combined warnings: ${combinedResult.metadata.warnings}")
        println("Combined context: ${combinedResult.metadata.context}")
        println("Combined metrics: ${combinedResult.metadata.metrics}")

        // Demonstrate Result.runCatching with metadata
        val catchingResult = Result.runCatching(
            Result.Metadata().addContext("operation", "risky")
        ) {
            "Successful operation"
        }

        println("\nRunCatching result: ${catchingResult.getOrNull()}")
        println("Operation context: ${catchingResult.metadata.context}")
    }
}

/**
 * Main function to run the examples.
 */
fun main() {
    println("🚀 BugreportParser Result Architecture Examples")
    println("=".repeat(50))
    
    ResultArchitectureExample.demonstrateResultUsage()
    ResultArchitectureExample.demonstrateErrorHandling()
    ResultArchitectureExample.demonstrateComposition()
    ResultArchitectureExample.demonstrateCustomResultFeatures()

    println("\n✨ Examples completed!")
}
